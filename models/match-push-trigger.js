const mongoose = require('mongoose');
const connectionLib = require('../lib/connection');

const schema = new mongoose.Schema({
  createdAt: { type: Date, default: () => Date.now() },
  triggerAt: { type: Date, required: true },
  user: { type: String, required: true },
  chatId: { type: mongoose.Schema.Types.ObjectId, required: true },
  matchedUser: { type: String },
  matchedUserName: { type: String },
  sharedInterestCount: { type: Number },
  configEnabled: { type: Boolean },
});

schema.index({ triggerAt: 1, user: 1 });
schema.index({ chatId: 1, user: 1 }, { unique: true });
schema.index({ user: 1 });
schema.index({ matchedUser: 1 });

let conn = connectionLib.getEventsConnection() || mongoose;
module.exports = conn.model('MatchPushTrigger', schema);
