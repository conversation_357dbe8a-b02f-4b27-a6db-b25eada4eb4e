const express = require('express');

const router = express.Router();
const asyncHandler = require('express-async-handler');
const pushLib = require('../lib/push');
const deleteLib = require('../lib/delete');
const socialLib = require('../lib/social');
const metricsLib = require('../lib/metrics');
const reviewsLib = require('../lib/reviews');
const costAnalyticsLib = require('../lib/cost-analytics');
const updateLib = require('../lib/update');
const qodUploadLib = require('../lib/qod-upload');
const { error } = require('../lib/error');
const { voidInvoices } = require('../lib/stripe');
const { setCacheImagesSocialProof } = require('../../lib/social-proof-cache')
const { processStaleBatches } = require('../lib/ai-image')
const { cleanupDeletedUsers } = require('../lib/cleanup-old-deleted-users');
const { deleteChats } = require('../lib/delete-chats');
const { fetchAppRanking } = require('../lib/st-app-ranking')

module.exports = function () {
  router.post('/', asyncHandler(async (req, res, next) => {
    // {"route": "dailyPush"}
    if (req.body.route === 'dailyPush') {
      await pushLib.dailyPush(req, res, next);
    }
    if (req.body.route === 'cityNewUserPush') {
      await pushLib.cityNewUserPush(req, res, next);
    }
    if (req.body.route === 'secondSale') {
      await pushLib.secondSale(req, res, next);
    }
    if (req.body.route === 'inactiveReminder') {
      await pushLib.inactiveReminder(req, res, next);
    }
    if (req.body.route === 'birthdayPush') {
      await pushLib.birthdayPush(req, res, next);
    }
    if (req.body.route === 'notifyFlashSaleExpiration') {
      await pushLib.notifyFlashSaleExpiration(req, res, next);
    }
    if (req.body.route === 'deleteUsers') {
      await deleteLib.deleteUsers(req, res, next);
    }
    if (req.body.route === 'deleteChats') {
      await deleteChats(req, res, next);
    }
    if (req.body.route === 'updateQuestionScores') {
      await socialLib.updateQuestionScores(req, res, next);
    }
    if (req.body.route === 'updateCommentScores') {
      await socialLib.updateCommentScores(req, res, next);
    }
    if (req.body.route === 'updateSitemap') {
      await socialLib.updateSitemap(req, res, next);
    }
    if (req.body.route === 'updateCountryFilterSitemaps') {
      await socialLib.updateCountryFilterSitemaps(req, res, next);
    }
    if (req.body.route === 'markDuplicateProfilesBySlugs') {
      await socialLib.markDuplicateProfilesBySlugs(req, res, next);
    }
    if (req.body.route === 'personalityDatabaseUpdate') {
      await socialLib.personalityDatabaseUpdate(req, res, next);
    }
    if (req.body.route === 'updateAutoIndexNowSitemap') {
      await socialLib.updateAutoIndexNowSitemap(req, res, next);
    }
    if (req.body.route === 'backfillInterestPoints') {
      await socialLib.backfillInterestPoints(req, res, next);
    }
    if (req.body.route === 'recordMetrics') {
      await metricsLib.recordMetrics(req, res, next);
    }
    if (req.body.route === 'recordCityMetrics') {
      await metricsLib.recordCityMetrics(req, res, next);
    }
    if (req.body.route === 'recordLanguageMetrics') {
      await metricsLib.recordLanguageMetrics(req, res, next);
    }
    if (req.body.route === 'recordInterestMetrics') {
      await metricsLib.recordInterestMetrics(req, res, next);
    }
    if (req.body.route === 'mark30DayInactive') {
      await metricsLib.mark30DayInactive(req, res, next);
    }
    if (req.body.route === 'recordATT') {
      await metricsLib.recordATT(req, res, next);
    }
    if (req.body.route === 'updateUserScores') {
      await pushLib.updateUserScores(req, res, next);
    }
    if (req.body.route === 'sendEmailNotifications') {
      await pushLib.sendEmailNotifications(req, res, next);
    }
    if (req.body.route === 'dailyDigest') {
      await pushLib.dailyDigest(req, res, next);
    }
    if (req.body.route === 'day1UniversesPush') {
      await pushLib.day1UniversesPush(req, res, next);
    }
    if (req.body.route === 'day1SpiritRealmPush') {
      await pushLib.day1SpiritRealmPush(req, res, next);
    }
    if (req.body.route === 'day1UnlimitedLovesPush') {
      await pushLib.day1UnlimitedLovesPush(req, res, next);
    }
    if (req.body.route === 'day2Push') {
      await pushLib.day2Push(req, res, next);
    }
    if (req.body.route === 'day3Push') {
      await pushLib.day3Push(req, res, next);
    }
    if (req.body.route === 'day4Push') {
      await pushLib.day4Push(req, res, next);
    }
    if (req.body.route === 'day5Push') {
      await pushLib.day5Push(req, res, next);
    }
    if (req.body.route === 'recurringMonthlySale') {
      await pushLib.recurringMonthlySale(req, res, next);
    }
    if (req.body.route === 'superLikeSale') {
      await pushLib.superLikeSale(req, res, next);
    }
    if (req.body.route === 'coinsSale') {
      await pushLib.coinsSale(req, res, next);
    }
    if (req.body.route === 'boostPromoD1') {
      await pushLib.boostPromoD1(req, res, next);
    }
    if (req.body.route === 'boostSaleD3') {
      await pushLib.boostSaleD3(req, res, next);
    }
    if (req.body.route === 'endBoostLiveActivity') {
      await pushLib.endBoostLiveActivity(req, res, next);
    }
    if (req.body.route === 'notifyBoostEffectiveness') {
      await pushLib.notifyBoostEffectiveness(req, res, next);
    }
    if (req.body.route === 'notifyConversationStarterReminder') {
      await pushLib.notifyConversationStarterReminder(req, res, next);
    }
    if (req.body.route === 'backFillPersonalityData') {
      await metricsLib.backFillPersonalityData(req, res, next);
    }
    if (req.body.route === 'updateDatabaseProfileScores') {
      await metricsLib.updateDatabaseProfileScores(req, res, next);
    }
    if (req.body.route === 'replyToReviews') {
      await reviewsLib.replyToReviews(req, res, next);
    }
    if (req.body.route === 'recordCohortCost') {
      await costAnalyticsLib.recordCohortCost(req, res, next);
    }
    if (req.body.route === 'updateUserAges') {
      await updateLib.updateUserAges(req, res, next);
    }
    if (req.body.route === 'grantCoinAwardsForReports') {
      await updateLib.grantCoinAwardsForReports(req, res, next);
    }
    if (req.body.route === 'voidInvoices') {
      await voidInvoices(req, res, next);
    }
    if (req.body.route === 'setCacheImagesSocialProof') {
      await setCacheImagesSocialProof(req, res, next);
    }
    if (req.body.route === 'cleanupDeletedUsers') {
      await cleanupDeletedUsers(req, res, next);
    }
    if (req.body.route === 'fetchAppRanking'){
      await fetchAppRanking(req, res, next)
    }
    if (req.body.route === 'executeQodUploadSheet') {
      await qodUploadLib.executeQodUploadSheet(req, res, next);
    }
    if (req.body.route === 'error'){
      await error(req, res, next);
    }
  }));

  router.post('/dailyPush', asyncHandler(pushLib.dailyPush));
  router.post('/cityNewUserPush', asyncHandler(pushLib.cityNewUserPush));
  router.post('/secondSale', asyncHandler(pushLib.secondSale));
  router.post('/inactiveReminder', asyncHandler(pushLib.inactiveReminder));
  router.post('/birthdayPush', asyncHandler(pushLib.birthdayPush));
  router.post('/notifyFlashSaleExpiration', asyncHandler(pushLib.notifyFlashSaleExpiration));
  router.post('/deleteUsers', asyncHandler(deleteLib.deleteUsers));
  router.post('/deleteChats', asyncHandler(deleteChats));
  router.post('/updateQuestionScores', asyncHandler(socialLib.updateQuestionScores));
  router.post('/updateCommentScores', asyncHandler(socialLib.updateCommentScores));
  router.post('/updateSitemap', asyncHandler(socialLib.updateSitemap));
  router.post('/updateCountryFilterSitemaps', asyncHandler(socialLib.updateCountryFilterSitemaps));
  router.post('/markDuplicateProfilesBySlugs', asyncHandler(socialLib.markDuplicateProfilesBySlugs));
  router.post('/personalityDatabaseUpdate', asyncHandler(socialLib.personalityDatabaseUpdate));
  router.post('/updateAutoIndexNowSitemap', asyncHandler(socialLib.updateAutoIndexNowSitemap));
  router.post('/recordMetrics', asyncHandler(metricsLib.recordMetrics));
  router.post('/recordCityMetrics', asyncHandler(metricsLib.recordCityMetrics));
  router.post('/recordLanguageMetrics', asyncHandler(metricsLib.recordLanguageMetrics));
  router.post('/recordInterestMetrics', asyncHandler(metricsLib.recordInterestMetrics));
  router.post('/mark30DayInactive', asyncHandler(metricsLib.mark30DayInactive));
  router.post('/recordATT', asyncHandler(metricsLib.recordATT));
  router.post('/updateUserScores', asyncHandler(pushLib.updateUserScores));
  router.post('/sendEmailNotifications', asyncHandler(pushLib.sendEmailNotifications));
  router.post('/dailyDigest', asyncHandler(pushLib.dailyDigest));
  router.post('/day1UniversesPush', asyncHandler(pushLib.day1UniversesPush));
  router.post('/day1SpiritRealmPush', asyncHandler(pushLib.day1SpiritRealmPush));
  router.post('/day1UnlimitedLovesPush', asyncHandler(pushLib.day1UnlimitedLovesPush));
  router.post('/day2Push', asyncHandler(pushLib.day2Push));
  router.post('/day3Push', asyncHandler(pushLib.day3Push));
  router.post('/day4Push', asyncHandler(pushLib.day4Push));
  router.post('/day5Push', asyncHandler(pushLib.day5Push));
  router.post('/recurringMonthlySale', asyncHandler(pushLib.recurringMonthlySale));
  router.post('/superLikeSale', asyncHandler(pushLib.superLikeSale));
  router.post('/coinsSale', asyncHandler(pushLib.coinsSale));
  router.post('/boostPromoD1', asyncHandler(pushLib.boostPromoD1));
  router.post('/boostSaleD3', asyncHandler(pushLib.boostSaleD3));
  router.post('/endBoostLiveActivity', asyncHandler(pushLib.endBoostLiveActivity));
  router.post('/notifyBoostEffectiveness', asyncHandler(pushLib.notifyBoostEffectiveness));
  router.post('/notifyConversationStarterReminder', asyncHandler(pushLib.notifyConversationStarterReminder));
  router.post('/backFillPersonalityData', asyncHandler(metricsLib.backFillPersonalityData));
  router.post('/updateDatabaseProfileScores', asyncHandler(metricsLib.updateDatabaseProfileScores));
  router.post('/replyToReviews', asyncHandler(reviewsLib.replyToReviews));
  router.post('/recordCohortCost', asyncHandler(costAnalyticsLib.recordCohortCost));
  router.post('/updateUserAges', asyncHandler(updateLib.updateUserAges));
  router.post('/grantCoinAwardsForReports', asyncHandler(updateLib.grantCoinAwardsForReports));
  router.post('/voidInvoices', asyncHandler(voidInvoices));
  router.post('/setCacheImagesSocialProof', asyncHandler(setCacheImagesSocialProof));
  router.post('/processAIImageStaleBatches', asyncHandler(processStaleBatches))
  router.post('/cleanupDeletedUsers', asyncHandler(cleanupDeletedUsers));
  router.post('/fetchAppRanking', asyncHandler(fetchAppRanking));
  router.post('/executeQodUploadSheet', asyncHandler(qodUploadLib.executeQodUploadSheet));
  router.post('/error', asyncHandler(error));

  return router;
};
